import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.fitmaster.app',
  appName: 'FitMaster',
  webDir: 'dist',
  server: {
    androidScheme: 'https',
    // 允许导航到外部URL
    allowNavigation: [
      '**************:8000',
      'localhost:*'
    ]
  },
  plugins: {
    // 状态栏插件 - 完全由StatusBarManager运行时控制
    StatusBar: {
      overlaysWebView: false // 只设置不覆盖WebView，其他由运行时控制
    },
    
    // 启动屏插件配置
    SplashScreen: {
      launchShowDuration: 2000,
      launchAutoHide: true,
      backgroundColor: "#ffffff",
      androidSplashResourceName: "splash",
      androidScaleType: "CENTER_CROP",
      showSpinner: false,
      androidSpinnerStyle: "large",
      iosSpinnerStyle: "small",
      spinnerColor: "#3b82f6",
      splashFullScreen: true,
      splashImmersive: true,
    },
    
    // CapacitorHttp插件配置 - 改善网络请求
    CapacitorHttp: {
      enabled: true
    }
  },
  
  // iOS特定配置
  ios: {
    // Safe Area和WebView配置
    contentInset: 'automatic',
    backgroundColor: '#ffffff',
    
    // 自定义用户代理
    appendUserAgent: 'FitMaster/1.0',
    
    // WebView配置优化
    webContentsDebuggingEnabled: true,
    
    // 网络请求配置
    scheme: 'capacitor'
  },
  
  // Android特定配置
  android: {
    // WebView最小版本
    minWebViewVersion: 60,
    
    // 允许混合内容
    allowMixedContent: true,
    
    // 构建选项
    buildOptions: {
      keystorePath: undefined,
      keystorePassword: undefined,
      keystoreAlias: undefined,
      keystoreAliasPassword: undefined,
      releaseType: 'AAB',
      signingType: 'jarsigner'
    }
  }
};

export const CAPACITOR_CONFIG = {
  appId: 'com.fitmaster.app',
  appName: 'FitMaster',
  webDir: 'dist',
  server: {
    // 使用HTTPS协议确保iOS安全要求
    url: 'https://**************:8000',
    // 忽略证书错误（仅用于开发，生产环境应使用有效证书）
    cleartext: false
  }
} as const;

export default config; 