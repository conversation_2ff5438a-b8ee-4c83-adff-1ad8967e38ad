import React, { useState } from 'react';
import './FeedPage.scss';

// Mock数据接口
interface FeedPost {
  id: string;
  user: {
    id: string;
    name: string;
    username: string;
    avatar: string;
    isVerified?: boolean;
  };
  content: {
    text?: string;
    workout?: {
      name: string;
      duration: number; // minutes
      exercises: string[];
      volume: number; // kg
      calories: number;
    };
    image?: string;
  };
  stats: {
    likes: number;
    comments: number;
    shares: number;
  };
  isLiked: boolean;
  timestamp: Date;
  location?: string;
}

// Mock Feed数据
const mockFeedData: FeedPost[] = [
  {
    id: 'post_1',
    user: {
      id: 'user_1',
      name: '<PERSON>',
      username: 'alex_fitness',
      avatar: '/api/placeholder/40/40',
      isVerified: true
    },
    content: {
      text: '今天的训练感觉特别棒！新的PR达成 🏋️‍♂️',
      workout: {
        name: 'Push Day - 胸肩三头',
        duration: 75,
        exercises: ['卧推', '肩上推举', '双杠臂屈伸'],
        volume: 1250,
        calories: 320
      }
    },
    stats: {
      likes: 24,
      comments: 8,
      shares: 3
    },
    isLiked: true,
    timestamp: new Date('2025-01-17T08:30:00'),
    location: '上海健身房'
  },
  {
    id: 'post_2',
    user: {
      id: 'user_2',
      name: 'Sarah Wang',
      username: 'sarah_strong',
      avatar: '/api/placeholder/40/40'
    },
    content: {
      text: '连续训练一个月的成果展示！坚持就是胜利 💪',
      image: '/api/placeholder/400/300'
    },
    stats: {
      likes: 56,
      comments: 12,
      shares: 8
    },
    isLiked: false,
    timestamp: new Date('2025-01-17T07:15:00')
  },
  {
    id: 'post_3',
    user: {
      id: 'user_3',
      name: 'Mike Zhang',
      username: 'mike_muscle',
      avatar: '/api/placeholder/40/40'
    },
    content: {
      text: '早上的晨跑结束，准备开始力量训练！',
      workout: {
        name: 'Full Body Workout',
        duration: 90,
        exercises: ['深蹲', '硬拉', '卧推', '引体向上'],
        volume: 1800,
        calories: 450
      }
    },
    stats: {
      likes: 18,
      comments: 5,
      shares: 2
    },
    isLiked: false,
    timestamp: new Date('2025-01-17T06:45:00'),
    location: '中央公园'
  }
];

const FeedPage: React.FC = () => {
  const [posts, setPosts] = useState<FeedPost[]>(mockFeedData);
  const [filter, setFilter] = useState<'all' | 'workouts' | 'following'>('all');

  const formatTimestamp = (date: Date): string => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}分钟前`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}小时前`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  const handleLike = (postId: string) => {
    setPosts(posts.map(post => {
      if (post.id === postId) {
        return {
          ...post,
          isLiked: !post.isLiked,
          stats: {
            ...post.stats,
            likes: post.isLiked ? post.stats.likes - 1 : post.stats.likes + 1
          }
        };
      }
      return post;
    }));
  };

  const filteredPosts = posts.filter(post => {
    switch (filter) {
      case 'workouts':
        return post.content.workout;
      case 'following':
        // 这里可以根据关注关系过滤
        return true;
      default:
        return true;
    }
  });

  return (
    <div className="feed-page">
      {/* Feed Header */}
      <div className="feed-header">
        <div className="feed-title">
  
          <p>发现健身伙伴的最新动态</p>
        </div>
        
        {/* Filter Tabs */}
        <div className="feed-filters">
          <button 
            className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            全部动态
          </button>
          <button 
            className={`filter-btn ${filter === 'workouts' ? 'active' : ''}`}
            onClick={() => setFilter('workouts')}
          >
            训练记录
          </button>
          <button 
            className={`filter-btn ${filter === 'following' ? 'active' : ''}`}
            onClick={() => setFilter('following')}
          >
            关注的人
          </button>
        </div>
      </div>

      {/* Create Post */}
      <div className="create-post-card">
        <div className="create-post-header">
          <div className="user-avatar">
            <img src="/api/placeholder/40/40" alt="Your avatar" />
          </div>
          <button className="create-post-input">
            分享你的健身动态...
          </button>
        </div>
        <div className="create-post-actions">
          <button className="post-action-btn">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M15 10L11 14L17 20L21 4L3 11L9 13V19L12 16"/>
            </svg>
            分享训练
          </button>
          <button className="post-action-btn">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <circle cx="8.5" cy="8.5" r="1.5"/>
              <path d="M21 15L16 10L5 21"/>
            </svg>
            上传图片
          </button>
        </div>
      </div>

      {/* Feed Posts */}
      <div className="feed-posts">
        {filteredPosts.map(post => (
          <article key={post.id} className="feed-post">
            {/* Post Header */}
            <div className="post-header">
              <div className="post-user">
                <div className="user-avatar">
                  <img src={post.user.avatar} alt={post.user.name} />
                </div>
                <div className="user-info">
                  <div className="user-name">
                    {post.user.name}
                    {post.user.isVerified && (
                      <svg className="verified-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2L13.09 5.26L16 6L13.09 6.74L12 10L10.91 6.74L8 6L10.91 5.26L12 2Z"/>
                        <path d="M12 12L13.09 15.26L16 16L13.09 16.74L12 20L10.91 16.74L8 16L10.91 15.26L12 12Z"/>
                      </svg>
                    )}
                  </div>
                  <div className="post-meta">
                    <span className="username">@{post.user.username}</span>
                    <span className="timestamp">{formatTimestamp(post.timestamp)}</span>
                    {post.location && (
                      <span className="location">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <path d="M21 10C21 17 12 23 12 23S3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.3639 3.63604C20.0518 5.32387 21 7.61305 21 10Z"/>
                          <circle cx="12" cy="10" r="3"/>
                        </svg>
                        {post.location}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <button className="post-menu-btn" aria-label="更多选项">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="12" cy="12" r="1"/>
                  <circle cx="19" cy="12" r="1"/>
                  <circle cx="5" cy="12" r="1"/>
                </svg>
              </button>
            </div>

            {/* Post Content */}
            <div className="post-content">
              {post.content.text && (
                <p className="post-text">{post.content.text}</p>
              )}
              
              {post.content.workout && (
                <div className="workout-summary">
                  <div className="workout-header">
                    <h4 className="workout-name">{post.content.workout.name}</h4>
                    <div className="workout-badge">训练记录</div>
                  </div>
                  <div className="workout-stats">
                    <div className="workout-stat">
                      <span className="stat-value">{post.content.workout.duration}</span>
                      <span className="stat-label">分钟</span>
                    </div>
                    <div className="workout-stat">
                      <span className="stat-value">{post.content.workout.volume}</span>
                      <span className="stat-label">kg</span>
                    </div>
                    <div className="workout-stat">
                      <span className="stat-value">{post.content.workout.calories}</span>
                      <span className="stat-label">卡路里</span>
                    </div>
                  </div>
                  <div className="workout-exercises">
                    <span className="exercises-label">主要动作:</span>
                    <span className="exercises-list">
                      {post.content.workout.exercises.join(' • ')}
                    </span>
                  </div>
                </div>
              )}
              
              {post.content.image && (
                <div className="post-image">
                  <img src={post.content.image} alt="Post image" />
                </div>
              )}
            </div>

            {/* Post Actions */}
            <div className="post-actions">
              <button 
                className={`action-btn like-btn ${post.isLiked ? 'liked' : ''}`}
                onClick={() => handleLike(post.id)}
              >
                <svg viewBox="0 0 24 24" fill={post.isLiked ? "currentColor" : "none"} stroke="currentColor">
                  <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.57831 8.50903 2.99871 7.05 2.99871C5.59096 2.99871 4.19169 3.57831 3.16 4.61C2.1283 5.64169 1.54871 7.04096 1.54871 8.5C1.54871 9.95904 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39468C21.7563 5.72725 21.351 5.12087 20.84 4.61Z"/>
                </svg>
                <span>{post.stats.likes}</span>
              </button>
              
              <button className="action-btn comment-btn">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z"/>
                </svg>
                <span>{post.stats.comments}</span>
              </button>
              
              <button className="action-btn share-btn">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="18" cy="5" r="3"/>
                  <circle cx="6" cy="12" r="3"/>
                  <circle cx="18" cy="19" r="3"/>
                  <path d="M8.59 13.51L15.42 17.49"/>
                  <path d="M15.41 6.51L8.59 10.49"/>
                </svg>
                <span>{post.stats.shares}</span>
              </button>
            </div>
          </article>
        ))}
      </div>

      {/* Load More */}
      <div className="load-more">
        <button className="load-more-btn">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z"/>
            <circle cx="12" cy="12" r="3"/>
          </svg>
          查看更多动态
        </button>
      </div>
    </div>
  );
};

export default FeedPage; 