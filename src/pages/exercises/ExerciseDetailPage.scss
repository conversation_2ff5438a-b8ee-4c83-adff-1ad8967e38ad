// 动作详情页面样式
.exercise-detail-page {
  min-height: 100vh;
  background: var(--bg-primary);
  
  // iOS Safe Area支持
  padding-bottom: calc(env(safe-area-inset-bottom) + var(--space-4));
}

// 页面头部
.exercise-detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  padding-top: calc(env(safe-area-inset-top) + var(--space-4));
  background: var(--bg-surface);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
  
  .header-spacer {
    flex: 1;
  }
  
  .back-btn {
    width: 44px;
    height: 44px;
    border-radius: var(--radius-full);
    background: var(--bg-secondary);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);
    
    svg {
      width: 20px;
      height: 20px;
      color: var(--text-primary);
      stroke-width: 2;
    }
    
    &:hover {
      background: var(--bg-hover);
      transform: scale(1.05);
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
  
  .header-actions {
    display: flex;
    gap: var(--space-2);
    
    .favorite-btn {
      width: 44px;
      height: 44px;
      border-radius: var(--radius-full);
      background: var(--bg-secondary);
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      
      svg {
        width: 20px;
        height: 20px;
        color: var(--text-secondary);
        stroke-width: 2;
      }
      
      &:hover {
        background: var(--accent-500);
        
        svg {
          color: var(--text-on-accent);
        }
      }
    }
  }
}

// 主要内容区域
.exercise-detail-content {
  padding: var(--space-4);
  
  // 各个部分的间距
  > * + * {
    margin-top: var(--space-6);
  }
}

// 动作标题部分
.exercise-title-section {
  text-align: center;
  
  .exercise-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
  }
  
  .exercise-meta {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--space-3);
    margin-top: var(--space-2);
    
    .meta-item {
      font-size: var(--text-sm);
      color: var(--text-secondary);
      background: var(--bg-secondary);
      padding: var(--space-1) var(--space-2);
      border-radius: var(--radius-sm);
      
      @media (max-width: 768px) {
        font-size: var(--text-xs);
      }
    }
  }
}

// 动作信息区域
.exercise-info-section {
  .exercise-info-card {
    background: var(--bg-surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-3);
    
    .exercise-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: var(--space-3);
      
      @media (max-width: 768px) {
        flex-wrap: wrap;
        gap: var(--space-2);
      }
      
      .meta-item {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        
        @media (max-width: 768px) {
          font-size: var(--text-xs);
        }
      }
      
      .difficulty {
        display: flex;
        align-items: center;
        
        .difficulty-plate {
          width: 20px;
          height: 20px;
          object-fit: contain;
          vertical-align: middle;
        }
      }
    }
  }
}

// 视频播放区域
.video-section {
  .video-container {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    cursor: pointer;
    
    .exercise-video {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: var(--radius-lg);
    }
    
    // 视频控制覆盖层
    .video-controls-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.3);
      opacity: 0;
      transition: opacity var(--transition-normal) var(--ease-in-out);
      pointer-events: none;
      
      .play-pause-btn {
        width: 60px;
        height: 60px;
        border-radius: var(--radius-full);
        background: rgba(255, 255, 255, 0.9);
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all var(--transition-normal) var(--ease-in-out);
        backdrop-filter: blur(4px);
        
        svg {
          width: 24px;
          height: 24px;
          color: var(--text-primary);
          stroke-width: 2;
        }
        
        &:hover {
          background: rgba(255, 255, 255, 1);
          transform: scale(1.1);
        }
        
        &.paused {
          svg {
            margin-left: 2px; // 播放图标向右偏移一点
          }
        }
      }
    }
    
    // 悬停时显示控制按钮
    &:hover .video-controls-overlay {
      opacity: 1;
      pointer-events: auto;
    }
    
    // 移动设备上点击时显示控制按钮
    &:active .video-controls-overlay {
      opacity: 1;
      pointer-events: auto;
    }
  }
}

// 训练部位信息区域
.muscle-info-section {
  h2 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 16px 0;
    text-align: left;
  }
  
  // 整体卡片容器
  .muscle-info-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    gap: 12px;
    align-items: flex-start;
    min-height: 300px;
    
    // 只有极小屏幕才改为上下布局
    @media (max-width: 480px) {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
      min-height: auto;
    }
  }
  
  // 左侧肌肉列表边栏（20%宽度）
  .muscle-list-sidebar {
    flex: 0 0 20%;
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 120px;
    
    // iOS端保持左右布局，减小最小宽度
    @media (max-width: 600px) {
      flex: 0 0 25%;
      min-width: 100px;
    }
    
    // 只有极小屏幕才改为100%宽度
    @media (max-width: 480px) {
      flex: none;
      width: 100%;
      min-width: auto;
    }
    
    .muscle-category {
      margin-bottom: 12px;
      
      h3 {
        font-size: 14px;
        font-weight: 700;
        color: #374151;
        margin: 0 0 6px 0;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        position: relative;
        padding-bottom: 6px;
        display: inline-block; // 让元素宽度适应文本
        width: auto; // 确保宽度自适应
        
        // 使用background-image作为下划线，与文字重合
        background-image: linear-gradient(transparent, transparent);
        background-repeat: no-repeat;
        background-size: 100% 5px; // 现在100%指的是文本宽度
        background-position: 0 calc(100% - 2px); // 与文字底部重合
        
        // 小屏幕下进一步缩小
        @media (max-width: 600px) {
          font-size: 13px;
          margin: 0 0 4px 0;
          padding-bottom: 4px;
        }
      }
      
      // 主要肌肉标题 - 蓝色下划线
      &:has(.muscle-tag.primary) h3 {
        background-image: linear-gradient(#3b82f6, #3b82f6);
      }
      
      // 次要肌肉标题 - 浅蓝色下划线  
      &:has(.muscle-tag.secondary) h3 {
        background-image: linear-gradient(#93c5fd, #93c5fd);
      }
      
      .muscle-tags {
        display: flex;
        flex-direction: column;
        gap: 4px;
        align-items: flex-start;
        
        .muscle-tag {
          display: inline-block;
          padding: 6px 12px;
          border-radius: 20px; // 使用半径大的圆弧框
          font-size: 12px;
          font-weight: 500;
          color: #374151; // 统一使用黑色字体
          background: #f3f4f6; // 统一使用灰色背景
          border: none;
          text-align: center;
          white-space: nowrap;
          width: fit-content;
          max-width: 100%;
          transition: all 0.2s ease;
          
          // 悬停效果
          &:hover {
            background: #e5e7eb;
            transform: translateY(-1px);
          }
          
          // 不再区分primary和secondary的背景色，统一使用灰色
          &.primary,
          &.secondary {
            background: #f3f4f6;
            color: #374151;
          }
          
          // 小屏幕下调整
          @media (max-width: 600px) {
            padding: 4px 10px;
            font-size: 11px;
          }
        }
      }
    }
  }
  
  // 右侧肌肉图容器（80%宽度）
  .muscle-illustration-container {
    flex: 0 0 80%;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 280px;
    
    // iOS端调整比例
    @media (max-width: 600px) {
      flex: 0 0 75%;
      min-height: 220px;
    }
    
    // 只有极小屏幕才改为100%宽度
    @media (max-width: 480px) {
      flex: none;
      width: 100%;
      min-height: 200px;
    }
    
    .no-muscle-data {
      text-align: center;
      color: #6b7280;
      
      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}

// 增强肌肉可视化组件样式
.enhanced-muscle-illustration {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  
  .muscle-svg {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }
  
  .muscle-legend {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    align-items: center;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      
      .legend-color {
        width: 16px;
        height: 16px;
        border-radius: var(--radius-sm);
        border: 1px solid var(--border-color);
      }
      
      span {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: var(--font-medium);
      }
    }
  }
}

.enhanced-muscle-illustration-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

// 动作指导区域
.instructions-section {
  h2 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-4) 0;
  }
  
  .instruction-steps {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    
    .instruction-step {
      display: flex;
      gap: var(--space-3);
      padding: var(--space-4);
      background: var(--bg-surface);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-lg);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:hover {
        border-color: var(--accent-300);
        background: var(--bg-hover);
      }
      
      &.active {
        border-color: var(--accent-500);
        background: var(--accent-50);
      }
      
      .step-number {
        width: 32px;
        height: 32px;
        border-radius: var(--radius-full);
        background: var(--accent-500);
        color: var(--text-on-accent);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-sm);
        font-weight: var(--font-semibold);
        flex-shrink: 0;
      }
      
      .step-content {
        flex: 1;
        color: var(--text-primary);
        font-size: var(--text-base);
        line-height: 1.6;
      }
    }
  }
}

// 注意事项区域
.tips-section {
  h2 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-4) 0;
  }
  
  .exercise-tips {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    
    .tip-item {
      display: flex;
      gap: var(--space-3);
      padding: var(--space-4);
      border-radius: var(--radius-lg);
      border-left: 4px solid;
      
      &.warning {
        background: var(--warning-50);
        border-left-color: var(--warning-500);
        
        .tip-icon svg {
          color: var(--warning-600);
        }
      }
      
      &.info {
        background: var(--info-50);
        border-left-color: var(--info-500);
        
        .tip-icon svg {
          color: var(--info-600);
        }
      }
      
      .tip-icon {
        flex-shrink: 0;
        
        svg {
          width: 20px;
          height: 20px;
          stroke-width: 2;
        }
      }
      
      .tip-content {
        flex: 1;
        color: var(--text-primary);
        font-size: var(--text-base);
        line-height: 1.6;
      }
    }
  }
}



// Loading和Error状态
.loading-state, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--space-8);
  text-align: center;
  
  .loading-spinner, .error-icon {
    margin-bottom: var(--space-4);
    
    svg {
      width: 48px;
      height: 48px;
      color: var(--text-secondary);
      
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
      
      &.spinning {
        animation: spin 1s linear infinite;
      }
    }
  }
  
  h3 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
  }
  
  p {
    color: var(--text-secondary);
    font-size: var(--text-base);
    margin: 0 0 var(--space-4) 0;
  }
  
  .error-actions {
    display: flex;
    gap: var(--space-3);
    
    .retry-btn, .back-btn-secondary {
      padding: var(--space-2) var(--space-4);
      border-radius: var(--radius-md);
      border: none;
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
    }
    
    .retry-btn {
      background: var(--accent-500);
      color: var(--text-on-accent);
      
      &:hover {
        background: var(--accent-400);
      }
    }
    
    .back-btn-secondary {
      background: var(--bg-secondary);
      color: var(--text-primary);
      
      &:hover {
        background: var(--bg-hover);
      }
    }
  }
}

// 响应式设计
@media (min-width: 768px) {
  .exercise-detail-content {
    max-width: 800px;
    margin: 0 auto;
  }
}

@media (min-width: 1024px) {
  .exercise-detail-content {
    max-width: 1000px;
  }
}

// 暗色主题支持
.theme-dark {
  // 重写颜色变量即可，CSS变量系统会自动适配
} 