// iOS 统一系统样式
// 基于 ScienceFit 样式指导的完整配色系统

:root {
  // === 浅色主题 (Light Theme) ===
  // 背景色系 - 基于 gray-50 到 gray-100
  --bg-primary: #ffffff;        /* 纯白主背景 */
  --bg-secondary: #f9fafb;      /* gray-50 次要背景 */
  --bg-tertiary: #f3f4f6;       /* gray-100 第三级背景 */
  --bg-surface: #ffffff;        /* 卡片表面背景 */
  --bg-card: #f9fafb;           /* 卡片内容背景 */
  --bg-hover: #f3f4f6;          /* 悬停背景 */
  
  // 文本色系 - 高对比度深色文本
  --text-primary: #1f2937;      /* gray-800 主文本 */
  --text-secondary: #4b5563;    /* gray-600 次要文本 */
  --text-tertiary: #6b7280;     /* gray-500 第三级文本 */
  --text-disabled: #9ca3af;     /* gray-400 禁用文本 */
  --text-on-accent: #ffffff;    /* 强调色背景上的文字 - 白色 */
  
  // 边框色系
  --border-color: #e5e7eb;      /* gray-200 边框 */
  --border-light: #f3f4f6;      /* gray-100 浅边框 */
  --border-heavy: #d1d5db;      /* gray-300 重边框 */
  
  // 强调色系 - 保持蓝色系
  --accent-500: #3b82f6;        /* 主强调色 */
  --accent-600: #2563eb;        /* 深强调色 */
  --accent-400: #60a5fa;        /* 浅强调色 */
  --accent-300: #93c5fd;        /* 更浅强调色 */
  
  // Primary 系列 - 用于非强调的UI元素
  --primary-400: #94a3b8;       /* slate-400 浅灰 */
  --primary-500: #64748b;       /* slate-500 中灰 */
  --primary-600: #475569;       /* slate-600 深灰 */
  --primary-700: #334155;       /* slate-700 更深灰 */
  
  // 功能色系
  --success-500: #22c55e;       /* 成功色 */
  --warning-500: #eab308;       /* 警告色 */
  --error-500: #ef4444;         /* 错误色 */
  
  // 阴影系统 - 浅色主题适用
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

// === 暗色主题 (Dark Theme) ===
.theme-dark {
  // 背景色系
  --bg-primary: #0f172a;        /* slate-900 主背景 */
  --bg-secondary: #1e293b;      /* slate-800 次要背景 */
  --bg-tertiary: #334155;       /* slate-700 第三级背景 */
  --bg-surface: #1e293b;        /* 卡片表面背景 */
  --bg-card: #334155;           /* 卡片内容背景 */
  --bg-hover: #475569;          /* 悬停背景 */
  
  // 文本色系
  --text-primary: #f8fafc;      /* slate-50 主文本 */
  --text-secondary: #cbd5e1;    /* slate-300 次要文本 */
  --text-tertiary: #94a3b8;     /* slate-400 第三级文本 */
  --text-disabled: #64748b;     /* slate-500 禁用文本 */
  --text-on-accent: #ffffff;    /* 强调色背景上的文字 - 白色 */
  
  // 边框色系
  --border-color: #374151;      /* gray-700 边框 */
  --border-light: #4b5563;      /* gray-600 浅边框 */
  --border-heavy: #6b7280;      /* gray-500 重边框 */
  
  // 强调色系 - 在暗色主题下稍微调亮
  --accent-500: #3b82f6;
  --accent-600: #2563eb;
  --accent-400: #60a5fa;
  --accent-300: #93c5fd;
  
  // Primary 系列 - 暗色主题下的适配
  --primary-400: #64748b;       /* slate-500 */
  --primary-500: #475569;       /* slate-600 */
  --primary-600: #334155;       /* slate-700 */
  --primary-700: #1e293b;       /* slate-800 */
  
  // 功能色系
  --success-500: #22c55e;
  --warning-500: #eab308;
  --error-500: #ef4444;
  
  // 阴影系统 - 暗色主题适用
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

// === 统一的语义化颜色别名 ===
:root, .theme-dark {
  // 导航相关
  --nav-bg: var(--bg-surface);
  --nav-text: var(--text-secondary);
  --nav-text-active: var(--accent-500);
  --nav-border: var(--border-color);
  
  // 卡片相关
  --card-bg: var(--bg-surface);
  --card-border: var(--border-color);
  --card-shadow: var(--shadow-md);
  
  // 按钮相关
  --btn-primary-bg: var(--accent-500);
  --btn-primary-text: #ffffff;
  --btn-primary-hover: var(--accent-600);
  
  // 输入框相关
  --input-bg: var(--bg-surface);
  --input-border: var(--border-color);
  --input-text: var(--text-primary);
  --input-placeholder: var(--text-tertiary);
  
  // 选中状态
  --selected-bg: var(--accent-500);
  --selected-text: #ffffff;
  --selected-border: var(--accent-500);
}

// === iOS Safe Area 支持 ===
.ios-safe-area {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

.ios-safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.ios-safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

// === 过渡动画 ===
.theme-transition {
  transition: 
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease,
    box-shadow 0.3s ease;
}

// === 高对比度支持 ===
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --border-color: #000000;
    --accent-500: #0066cc;
  }
  
  .theme-dark {
    --text-primary: #ffffff;
    --border-color: #ffffff;
    --accent-500: #66b3ff;
  }
}

// === 减少动画支持 ===
@media (prefers-reduced-motion: reduce) {
  .theme-transition {
    transition: none;
  }
} 