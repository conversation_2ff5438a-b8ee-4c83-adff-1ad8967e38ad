import React, { memo } from 'react';
import { Exercise } from '../../utils/exerciseDataMapper';
import { useLazyImage } from '../../hooks/useInfiniteScroll';
import likeIcon from '../../assets/like.png'; // 导入like图片
import './ExerciseCard.scss';

interface ExerciseCardProps {
  exercise: Exercise;
  onToggleFavorite: (exerciseId: string) => void;
  onExerciseClick?: (exercise: Exercise) => void;
  getDifficultyDisplay: (difficulty: string) => React.ReactNode;
}

/**
 * 优化的运动卡片组件
 * 支持图片懒加载和性能优化
 */
export const ExerciseCard: React.FC<ExerciseCardProps> = memo(({
  exercise,
  onToggleFavorite,
  onExerciseClick,
  getDifficultyDisplay
}) => {
  // 修改图像显示策略：优先gif_url，然后是image_url
  const getImageUrl = () => {
    const baseUrl = 'http://124.222.91.101:8000/exercises';
    
    if (exercise.gif_url) {
      // 如果有gif_url，使用gifs路径
      return `${baseUrl}/gifs/${exercise.gif_url}`;
    } else if (exercise.images && exercise.images.length > 0) {
      // 否则使用images路径
      const imageName = exercise.images[0].split('/').pop(); // 提取文件名
      return `${baseUrl}/images/${imageName}`;
    }
    
    return '';
  };

  const { imageSrc, imageRef, isLoaded } = useLazyImage(getImageUrl());

  const handleCardClick = () => {
    onExerciseClick?.(exercise);
  };

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleFavorite(exercise.id);
  };

  // 获取主要身体部位（取第一个）
  const primaryBodyPart = exercise.primary_muscles && exercise.primary_muscles.length > 0 
    ? exercise.primary_muscles[0] 
    : exercise.target; // 降级到target字段

  return (
    <div className="exercise-card" onClick={handleCardClick}>
      {/* Exercise Image */}
      <div className="exercise-image">
        <div className="image-container">
          {!isLoaded && <div className="image-placeholder" />}
          <img 
            ref={imageRef}
            src={imageSrc || undefined}
            alt={exercise.name}
            className={`exercise-img ${isLoaded ? 'loaded' : ''}`}
            loading="lazy"
          />
        </div>
        
        <button 
          className={`favorite-btn ${exercise.is_favorite ? 'favorited' : ''}`}
          onClick={handleFavoriteClick}
          aria-label={exercise.is_favorite ? '取消收藏' : '添加收藏'}
        >
          {exercise.is_favorite ? (
            <img src={likeIcon} alt="已收藏" className="like-icon" />
          ) : (
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.57831 8.50903 2.99871 7.05 2.99871C5.59096 2.99871 4.19169 3.57831 3.16 4.61C2.1283 5.64169 1.54871 7.04096 1.54871 8.5C1.54871 9.95904 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39468C21.7563 5.72725 21.351 5.12087 20.84 4.61Z"/>
            </svg>
          )}
        </button>
      </div>

      {/* Exercise Info */}
      <div className="exercise-info">
        {/* 动作名称和器械标签同行 */}
        <div className="exercise-header">
          <h3 className="exercise-name">{exercise.name}</h3>
          {exercise.equipment && exercise.equipment.length > 0 && (
            <div className="equipment-tags">
              <span className="equipment-tag">
                {exercise.equipment[0]}
                {exercise.equipment.length > 1 && ` +${exercise.equipment.length - 1}`}
              </span>
            </div>
          )}
        </div>
        
        {/* 难度显示和身体部位 */}
        <div className="exercise-meta">
          <div className="difficulty">{getDifficultyDisplay(exercise.difficulty)}</div>
          {primaryBodyPart && (
            <div className="body-parts">
              <span className="body-part-tag">{primaryBodyPart}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

ExerciseCard.displayName = 'ExerciseCard';