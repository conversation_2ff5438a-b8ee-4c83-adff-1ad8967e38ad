import React, { useMemo } from 'react';
import { MuscleGroupEnum } from '../../../types/muscle.types';
import { shouldUseHardwareAcceleration } from '../../../utils/muscleUtils';
import './SimpleMuscleIllustration.scss';
import { 
  ChestGroup, 
  AbsGroup, 
  BicepsGroup, 
  ShouldersGroup, 
  TricepsGroup,
  BackGroup,
  CalvesGroup,
  ForearmsGroup,
  GlutesGroup,
  HamstringsGroup,
  ObliquesGroup,
  QuadricepsGroup,
  TrapsGroup
} from './MuscleGroups';

// 简化的肌肉可视化组件接口
interface SimpleMuscleIllustrationProps {
  selectedMuscles: MuscleGroupEnum[];
  muscleColorConfig?: { [key: string]: 'primary' | 'secondary' };
  theme?: 'light' | 'dark';
  className?: string;
}

// 肌肉群颜色配置
const MUSCLE_COLORS = {
  primary: '#3b82f6',   // 主要肌肉 - 深蓝色
  secondary: '#93c5fd', // 次要肌肉 - 浅蓝色
  default: '#e2e8f0'    // 未选中 - 浅灰色
};

export const SimpleMuscleIllustration: React.FC<SimpleMuscleIllustrationProps> = ({
  selectedMuscles,
  muscleColorConfig = {},
  theme = 'light',
  className = ''
}) => {
  const useHardwareAccel = shouldUseHardwareAcceleration();

  // 肌肉样式生成函数
  const getMuscleClasses = useMemo(() => {
    return (muscle: MuscleGroupEnum): string => {
      const isSelected = selectedMuscles.includes(muscle);
      return isSelected ? 'muscle-selected' : 'muscle-default';
    };
  }, [selectedMuscles]);

  // 禁用肌肉点击交互
  const handleToggleMuscle = () => {
    // 不执行任何操作，去除交互功能
  };

  // SVG样式优化
  const svgStyles = useMemo(() => ({
    width: '100%',
    height: 'auto',
    maxWidth: '500px',
    // iOS硬件加速
    ...(useHardwareAccel && {
      willChange: 'transform',
      transform: 'translateZ(0)',
      backfaceVisibility: 'hidden' as const,
      perspective: 1000
    })
  }), [useHardwareAccel]);

  return (
    <div className={`simple-muscle-illustration ${theme} ${className}`}>
      <div className="muscle-body-container">
        <div className="muscle-svg-wrapper">
          <svg
            viewBox="0 0 535 462"
            className="muscle-illustration-simple"
            style={svgStyles}
            xmlns="http://www.w3.org/2000/svg"
          >
            {/* SVG背景定义 */}
            <defs>
              <style>
                {`
                  .muscle-selected {
                    fill: ${MUSCLE_COLORS.primary};
                    opacity: 0.8;
                  }
                  
                  .muscle-default {
                    fill: ${MUSCLE_COLORS.default};
                    opacity: 0.3;
                  }
                  
                  .body-outline {
                    fill: none;
                    stroke: #94a3b8;
                    stroke-width: 2;
                  }
                `}
              </style>
            </defs>

            {/* 人体轮廓 */}
            <path
              className="body-outline"
              d="M132,50 C120,45 110,55 108,65 L105,75 C100,85 95,95 95,105 L92,115 C90,125 88,135 90,145 L95,155 C100,165 105,175 110,185 L115,195 C120,205 125,215 130,225 L135,235 C140,245 145,255 150,265 L155,275 C160,285 165,295 170,305 L175,315 C180,325 185,335 190,345 L195,355 C200,365 205,375 210,385 L215,395 C220,405 225,415 230,425 L235,435 C240,445 245,455 250,462 L285,462 C290,455 295,445 300,435 L305,425 C310,415 315,405 320,395 L325,385 C330,375 335,365 340,355 L345,345 C350,335 355,325 360,315 L365,305 C370,295 375,285 380,275 L385,265 C390,255 395,245 400,235 L405,225 C410,215 415,205 420,195 L425,185 C430,175 435,165 440,155 L445,145 C447,135 445,125 443,115 L440,105 C440,95 435,85 430,75 L427,65 C425,55 415,45 403,50 L132,50 Z"
              fill="#757575"
              stroke="black"
              strokeWidth="0"
            />

            {/* 渲染所有肌肉群组件 */}
            <ChestGroup onToggleMuscle={handleToggleMuscle} getMuscleClasses={getMuscleClasses} />
            <AbsGroup onToggleMuscle={handleToggleMuscle} getMuscleClasses={getMuscleClasses} />
            <BicepsGroup onToggleMuscle={handleToggleMuscle} getMuscleClasses={getMuscleClasses} />
            <TricepsGroup onToggleMuscle={handleToggleMuscle} getMuscleClasses={getMuscleClasses} />
            <ShouldersGroup onToggleMuscle={handleToggleMuscle} getMuscleClasses={getMuscleClasses} />
            <BackGroup onToggleMuscle={handleToggleMuscle} getMuscleClasses={getMuscleClasses} />
            <TrapsGroup onToggleMuscle={handleToggleMuscle} getMuscleClasses={getMuscleClasses} />
            <QuadricepsGroup onToggleMuscle={handleToggleMuscle} getMuscleClasses={getMuscleClasses} />
            <HamstringsGroup onToggleMuscle={handleToggleMuscle} getMuscleClasses={getMuscleClasses} />
            <GlutesGroup onToggleMuscle={handleToggleMuscle} getMuscleClasses={getMuscleClasses} />
            <CalvesGroup onToggleMuscle={handleToggleMuscle} getMuscleClasses={getMuscleClasses} />
            <ForearmsGroup onToggleMuscle={handleToggleMuscle} getMuscleClasses={getMuscleClasses} />
            <ObliquesGroup onToggleMuscle={handleToggleMuscle} getMuscleClasses={getMuscleClasses} />
          </svg>
        </div>
      </div>
    </div>
  );
}; 