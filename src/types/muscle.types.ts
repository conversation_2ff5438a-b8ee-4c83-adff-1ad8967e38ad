// 肌肉群枚举定义 - 支持前后视图独立选择
export enum MuscleGroupEnum {
  // 前视图肌肉群
  BICEPS = "BICEPS",
  CHEST = "CHEST",
  SHOULDERS_FRONT = "SHOULDERS_FRONT",
  QUADRICEPS = "QUADRICEPS",
  ABDOMINALS = "ABDOMINALS",
  OBLIQUES = "OBLIQUES",
  FOREARMS_FRONT = "FOREARMS_FRONT",
  CALVES_FRONT = "CALVES_FRONT",
  HIP_ADDUCTORS = "HIP_ADDUCTORS",
  TENSOR_FASCIAE_FEMORIS = "TENSOR_FASCIAE_FEMORIS",
  TIBIAS = "TIBIAS",
  // 后视图肌肉群
  TRICEPS = "TRICEPS",
  BACK = "BACK",
  SHOULDERS_BACK = "SHOULDERS_BACK",
  HAMSTRINGS = "HAMSTRINGS",
  GLUTES = "GLUTES",
  TRAPS = "TRAPS",
  LATS = "LATS",
  LOWER_BACK = "LOWER_BACK",
  FOREARMS_BACK = "FOREARMS_BACK",
  CALVES_BACK = "CALVES_BACK",
  
  // 保留向后兼容的枚举值
  SHOULDERS = "SHOULDERS",
  FOREARMS = "FOREARMS",
  CALVES = "CALVES"
}

// 肌肉群中文名称映射
export const MUSCLE_NAMES: Record<MuscleGroupEnum, string> = {
  // 前视图肌肉群
  [MuscleGroupEnum.BICEPS]: "二头肌",
  [MuscleGroupEnum.CHEST]: "胸肌",
  [MuscleGroupEnum.SHOULDERS_FRONT]: "前肩",
  [MuscleGroupEnum.QUADRICEPS]: "股四头肌",
  [MuscleGroupEnum.ABDOMINALS]: "腹肌",
  [MuscleGroupEnum.OBLIQUES]: "腹斜肌",
  [MuscleGroupEnum.FOREARMS_FRONT]: "前臂前侧",
  [MuscleGroupEnum.CALVES_FRONT]: "小腿前侧",
  [MuscleGroupEnum.HIP_ADDUCTORS]: "内收肌",
  [MuscleGroupEnum.TENSOR_FASCIAE_FEMORIS]: "股筋膜张肌",
  [MuscleGroupEnum.TIBIAS]: "胫骨肌",
  // 后视图肌肉群
  [MuscleGroupEnum.TRICEPS]: "三头肌",
  [MuscleGroupEnum.BACK]: "背肌",
  [MuscleGroupEnum.SHOULDERS_BACK]: "后肩",
  [MuscleGroupEnum.HAMSTRINGS]: "腘绳肌",
  [MuscleGroupEnum.GLUTES]: "臀肌",
  [MuscleGroupEnum.TRAPS]: "斜方肌",
  [MuscleGroupEnum.LATS]: "背阔肌",
  [MuscleGroupEnum.LOWER_BACK]: "下背部",
  [MuscleGroupEnum.FOREARMS_BACK]: "前臂后侧",
  [MuscleGroupEnum.CALVES_BACK]: "小腿后侧",
  
  // 向后兼容
  [MuscleGroupEnum.SHOULDERS]: "肩部",
  [MuscleGroupEnum.FOREARMS]: "前臂",
  [MuscleGroupEnum.CALVES]: "小腿肌"
};

// 肌肉可视化主组件属性接口
export interface MuscleVisualizationProps {
  selectedMuscles: MuscleGroupEnum[];
  onMuscleSelect?: (muscle: MuscleGroupEnum) => void;
  interactive?: boolean;
  size?: 'sm' | 'md' | 'lg';
  theme?: 'light' | 'dark';
  className?: string;
}

// 肌肉群组件通用属性接口
export interface MuscleGroupProps {
  muscle: MuscleGroupEnum;
  isSelected: boolean;
  onSelect?: (muscle: MuscleGroupEnum) => void;
  interactive?: boolean;
  theme?: 'light' | 'dark';
  className?: string;
}

// 肌肉图谱插画组件属性接口
export interface MuscleIllustrationProps {
  selectedMuscles: MuscleGroupEnum[];
  onMuscleSelect?: (muscle: MuscleGroupEnum) => void;
  interactive?: boolean;
  width: number;
  height: number;
  theme?: 'light' | 'dark';
  className?: string;
}

// 肌肉卡片组件属性接口
export interface MuscleCardProps {
  primaryMuscles: string[];
  secondaryMuscles?: string[];
  size?: 'sm' | 'md';
  showVisualization?: boolean;
  interactive?: boolean;
  className?: string;
}

// 肌肉选择Hook返回类型
export interface MuscleVisualizationHook {
  selectedMuscles: MuscleGroupEnum[];
  toggleMuscle: (muscle: MuscleGroupEnum) => void;
  clearMuscles: () => void;
  setMuscles: (muscles: MuscleGroupEnum[]) => void;
  muscleCount: number;
  hasMuscle: (muscle: MuscleGroupEnum) => boolean;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

// 肌肉群显示名称映射类型
export type MuscleDisplayNameMap = Record<MuscleGroupEnum, string>;

// 肌肉群颜色配置类型
export interface MuscleGroupColors {
  selected: {
    light: string;
    dark: string;
  };
  unselected: {
    light: string;
    dark: string;
  };
  hover: {
    light: string;
    dark: string;
  };
}

// iOS优化配置类型
export interface iOSMuscleConfig {
  optimalSize: 'sm' | 'md' | 'lg';
  useHardwareAcceleration: boolean;
  enableTouchFeedback: boolean;
  safeAreaInsets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

// === 🔄 muscle-selector-complete 集成支持 ===

// 导入muscle-selector-complete的类型定义
import { MuscleGroup as SelectorMuscleGroup } from '../components/muscle-selector-complete/src/types';

// 暂时移除muscle-selector-complete映射，专注于修复前后视图问题
// TODO: 重新实现muscle-selector-complete集成
export const MUSCLE_GROUP_MAPPING: Partial<Record<MuscleGroupEnum, any>> = {};

// 暂时移除反向映射，专注于修复前后视图问题
// TODO: 重新实现muscle-selector-complete集成
export const SELECTOR_TO_SYSTEM_MAPPING: Partial<Record<any, MuscleGroupEnum>> = {};

// 类型转换工具函数
export const muscleGroupConverters = {
  // 现有系统 -> muscle-selector-complete
  toSelector: (muscle: MuscleGroupEnum): SelectorMuscleGroup | null => {
    return MUSCLE_GROUP_MAPPING[muscle];
  },

  // muscle-selector-complete -> 现有系统
  fromSelector: (muscle: any): MuscleGroupEnum => {
    return SELECTOR_TO_SYSTEM_MAPPING[muscle] || MuscleGroupEnum.CHEST;
  },

  // 批量转换：现有系统 -> muscle-selector-complete
  toSelectorArray: (muscles: MuscleGroupEnum[]): SelectorMuscleGroup[] => {
    return muscles.map(muscle => MUSCLE_GROUP_MAPPING[muscle]).filter(Boolean) as SelectorMuscleGroup[];
  },

  // 批量转换：muscle-selector-complete -> 现有系统
  fromSelectorArray: (muscles: any[]): MuscleGroupEnum[] => {
    return muscles.map(muscle => SELECTOR_TO_SYSTEM_MAPPING[muscle] || MuscleGroupEnum.CHEST);
  },

  // 检查肌肉群是否被muscle-selector-complete支持
  isSelectorSupported: (muscle: MuscleGroupEnum): boolean => {
    return MUSCLE_GROUP_MAPPING[muscle] !== null;
  },

  // 获取所有被支持的肌肉群
  getSupportedMuscles: (): MuscleGroupEnum[] => {
    return Object.keys(MUSCLE_GROUP_MAPPING).filter(
      muscle => MUSCLE_GROUP_MAPPING[muscle as MuscleGroupEnum] !== null
    ) as MuscleGroupEnum[];
  },

  // 获取不被支持的肌肉群（需要自定义处理）
  getUnsupportedMuscles: (): MuscleGroupEnum[] => {
    return Object.keys(MUSCLE_GROUP_MAPPING).filter(
      muscle => MUSCLE_GROUP_MAPPING[muscle as MuscleGroupEnum] === null
    ) as MuscleGroupEnum[];
  }
};

// 统一的肌肉群接口（兼容两个系统）
export interface UnifiedMuscleGroup {
  id: string;
  name: string;
  systemType: MuscleGroupEnum;
  selectorType: SelectorMuscleGroup | null;
  isSupported: boolean;
  displayName: string;
}

// 肌肉群显示名称映射
export const MUSCLE_DISPLAY_NAMES: Partial<Record<MuscleGroupEnum, string>> = {
  [MuscleGroupEnum.CHEST]: '胸肌',
  [MuscleGroupEnum.BACK]: '背部',
  [MuscleGroupEnum.SHOULDERS]: '肩部',
  [MuscleGroupEnum.SHOULDERS_FRONT]: '前肩',
  [MuscleGroupEnum.SHOULDERS_BACK]: '后肩',
  [MuscleGroupEnum.BICEPS]: '二头肌',
  [MuscleGroupEnum.TRICEPS]: '三头肌',
  [MuscleGroupEnum.FOREARMS]: '前臂',
  [MuscleGroupEnum.FOREARMS_FRONT]: '前臂前侧',
  [MuscleGroupEnum.FOREARMS_BACK]: '前臂后侧',
  [MuscleGroupEnum.ABDOMINALS]: '腹肌',
  [MuscleGroupEnum.OBLIQUES]: '腹斜肌',
  [MuscleGroupEnum.QUADRICEPS]: '股四头肌',
  [MuscleGroupEnum.HAMSTRINGS]: '腘绳肌',
  [MuscleGroupEnum.GLUTES]: '臀部',
  [MuscleGroupEnum.CALVES]: '小腿',
  [MuscleGroupEnum.CALVES_FRONT]: '小腿前侧',
  [MuscleGroupEnum.CALVES_BACK]: '小腿后侧',
  [MuscleGroupEnum.TRAPS]: '斜方肌',
  [MuscleGroupEnum.LATS]: '背阔肌',
  [MuscleGroupEnum.LOWER_BACK]: '下背部'
};

// 创建统一的肌肉群对象
export const createUnifiedMuscleGroups = (): UnifiedMuscleGroup[] => {
  return Object.values(MuscleGroupEnum).map(muscle => ({
    id: muscle,
    name: muscle,
    systemType: muscle,
    selectorType: MUSCLE_GROUP_MAPPING[muscle],
    isSupported: MUSCLE_GROUP_MAPPING[muscle] !== null,
    displayName: MUSCLE_DISPLAY_NAMES[muscle] || muscle
  }));
}; 