import { ApiExerciseResponse, ExerciseSearchParams, buildExerciseQueryParams } from '../utils/exerciseDataMapper';
import { ExerciseDetailResponse } from '../types/exercise.types';
import { getApiConfig, getPlatformNetworkConfig } from '../config/api.config';

/**
 * FitMaster API服务层
 * 统一管理所有后端API调用，支持认证、错误处理、重试机制和离线缓存
 */

// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 认证相关类型
export interface AuthTokens {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
}

export interface LoginCredentials {
  username: string
  password: string
}

export interface UserProfile {
  id: string
  username: string
  email: string
  display_name: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

// 健身数据相关类型
export interface FitnessData {
  date: string
  move_progress: number
  move_goal: number
  exercise_progress: number
  exercise_goal: number
  stand_progress: number
  stand_goal: number
  calories_burned: number
  steps_taken: number
  water_intake: number
}

export interface WorkoutData {
  id?: string
  name: string
  start_time: string
  end_time?: string
  duration: number // minutes
  exercises: Exercise[]
  total_volume: number // kg
  calories_burned: number
  notes?: string
}

export interface Exercise {
  id: string
  name: string
  category: string
  muscle_groups: string[]
  equipment: string[]
  sets: ExerciseSet[]
}

export interface ExerciseSet {
  set_number: number
  reps: number
  weight: number
  rest_time?: number
  notes?: string
  completed: boolean
}

// 错误处理类型
export enum ApiErrorType {
  NETWORK_ERROR = 'network_error',
  AUTH_ERROR = 'auth_error',
  VALIDATION_ERROR = 'validation_error',
  SERVER_ERROR = 'server_error',
  TIMEOUT_ERROR = 'timeout_error'
}

export interface ApiError {
  type: ApiErrorType
  message: string
  status?: number
  details?: any
}

class ApiService {
  private config: ReturnType<typeof getApiConfig>
  private accessToken: string | null = null
  private refreshToken: string | null = null
  private networkConfig = getPlatformNetworkConfig()

  constructor() {
    this.config = getApiConfig()

    // 输出平台和网络配置信息用于调试
    if (this.config.enableLogging) {
      console.log('🔧 FitMaster API Configuration:', {
        platform: this.networkConfig.platform,
        isNative: this.networkConfig.isNative,
        baseURL: this.config.baseURL,
        timeout: this.config.timeout
      });

      if (this.networkConfig.platform === 'ios' && this.config.baseURL.startsWith('http:')) {
        console.warn('⚠️ iOS检测到HTTP连接，请确保Info.plist已配置App Transport Security');
      }
    }

    // 从本地存储恢复token
    this.loadTokensFromStorage()
  }

  /**
   * Token管理
   */
  private loadTokensFromStorage(): void {
    const tokens = localStorage.getItem('fitmaster_auth_tokens')
    if (tokens) {
      try {
        const parsedTokens = JSON.parse(tokens)
        this.accessToken = parsedTokens.access_token
        this.refreshToken = parsedTokens.refresh_token
      } catch (error) {
        console.warn('Failed to parse stored tokens:', error)
        this.clearTokens()
      }
    }
  }

  private saveTokensToStorage(tokens: AuthTokens): void {
    localStorage.setItem('fitmaster_auth_tokens', JSON.stringify(tokens))
    this.accessToken = tokens.access_token
    this.refreshToken = tokens.refresh_token
  }

  private clearTokens(): void {
    localStorage.removeItem('fitmaster_auth_tokens')
    this.accessToken = null
    this.refreshToken = null
  }

  /**
   * 基础HTTP请求方法
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    skipAuth = false
  ): Promise<T> {
    const url = `${this.config.baseURL}${endpoint}`
    
    let attempt = 0
    while (attempt <= this.config.retries) {
      try {
        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          ...options.headers as Record<string, string>
        }

        // 添加认证头
        if (!skipAuth && this.accessToken) {
          headers.Authorization = `Bearer ${this.accessToken}`
        }

        const config: RequestInit = {
          ...options,
          headers,
          signal: AbortSignal.timeout(this.config.timeout)
        }

        const response = await fetch(url, config)

        // 处理401未授权错误
        if (response.status === 401 && !skipAuth && this.refreshToken) {
          const refreshed = await this.refreshAccessToken()
          if (refreshed) {
            // 重试原始请求
            return this.request<T>(endpoint, options, skipAuth)
          } else {
            throw new Error('Authentication failed')
          }
        }

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const data = await response.json()
        return data

      } catch (error) {
        attempt++
        
        if (attempt > this.config.retries) {
          throw this.handleError(error)
        }

        // 指数退避重试
        const delay = this.config.retryDelay * Math.pow(2, attempt - 1)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    throw new Error('Max retries exceeded')
  }

  /**
   * 错误处理
   */
  private handleError(error: any): ApiError {
    if (error.name === 'AbortError' || error.message?.includes('timeout')) {
      return {
        type: ApiErrorType.TIMEOUT_ERROR,
        message: '请求超时，请检查网络连接',
        details: error
      }
    }

    if (error.message?.includes('fetch')) {
      return {
        type: ApiErrorType.NETWORK_ERROR,
        message: '网络连接失败，请检查网络设置',
        details: error
      }
    }

    if (error.message?.includes('401')) {
      return {
        type: ApiErrorType.AUTH_ERROR,
        message: '认证失败，请重新登录',
        details: error
      }
    }

    if (error.message?.includes('400')) {
      return {
        type: ApiErrorType.VALIDATION_ERROR,
        message: '请求参数错误',
        details: error
      }
    }

    if (error.message?.includes('500')) {
      return {
        type: ApiErrorType.SERVER_ERROR,
        message: '服务器内部错误，请稍后重试',
        details: error
      }
    }

    return {
      type: ApiErrorType.SERVER_ERROR,
      message: '未知错误，请联系技术支持',
      details: error
    }
  }

  /**
   * Token刷新
   */
  private async refreshAccessToken(): Promise<boolean> {
    if (!this.refreshToken) return false

    try {
      const response = await this.request<AuthTokens>(
        '/auth/refresh',
        {
          method: 'POST',
          body: JSON.stringify({ refresh_token: this.refreshToken })
        },
        true // 跳过认证
      )

      this.saveTokensToStorage(response)
      return true
    } catch (error) {
      console.warn('Token refresh failed:', error)
      this.clearTokens()
      return false
    }
  }

  /**
   * 认证API
   */
  async login(credentials: LoginCredentials): Promise<AuthTokens> {
    const response = await this.request<AuthTokens>(
      '/auth/login',
      {
        method: 'POST',
        body: JSON.stringify(credentials)
      },
      true
    )

    this.saveTokensToStorage(response)
    return response
  }

  async logout(): Promise<void> {
    try {
      await this.request('/auth/logout', {
        method: 'POST'
      })
    } catch (error) {
      console.warn('Logout API call failed:', error)
    } finally {
      this.clearTokens()
    }
  }

  async register(userData: {
    username: string
    email: string
    password: string
    display_name?: string
  }): Promise<UserProfile> {
    return this.request<UserProfile>(
      '/auth/register',
      {
        method: 'POST',
        body: JSON.stringify(userData)
      },
      true
    )
  }

  /**
   * 用户资料API
   */
  async getUserProfile(): Promise<UserProfile> {
    return this.request<UserProfile>('/user/profile')
  }

  async updateUserProfile(updates: Partial<UserProfile>): Promise<UserProfile> {
    return this.request<UserProfile>(
      '/user/profile',
      {
        method: 'PUT',
        body: JSON.stringify(updates)
      }
    )
  }

  /**
   * 健身数据API
   */
  async getFitnessData(date: string): Promise<FitnessData> {
    return this.request<FitnessData>(`/fitness/data?date=${date}`)
  }

  async updateFitnessData(date: string, data: Partial<FitnessData>): Promise<FitnessData> {
    return this.request<FitnessData>(
      `/fitness/data`,
      {
        method: 'POST',
        body: JSON.stringify({ date, ...data })
      }
    )
  }

  async getFitnessDataRange(startDate: string, endDate: string): Promise<FitnessData[]> {
    return this.request<FitnessData[]>(
      `/fitness/data/range?start_date=${startDate}&end_date=${endDate}`
    )
  }

  /**
   * 训练记录API
   */
  async getWorkouts(
    limit = 20,
    offset = 0,
    startDate?: string,
    endDate?: string
  ): Promise<WorkoutData[]> {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString()
    })
    
    if (startDate) params.append('start_date', startDate)
    if (endDate) params.append('end_date', endDate)

    return this.request<WorkoutData[]>(`/workouts?${params}`)
  }

  async getWorkout(workoutId: string): Promise<WorkoutData> {
    return this.request<WorkoutData>(`/workouts/${workoutId}`)
  }

  async createWorkout(workoutData: Omit<WorkoutData, 'id'>): Promise<WorkoutData> {
    return this.request<WorkoutData>(
      '/workouts',
      {
        method: 'POST',
        body: JSON.stringify(workoutData)
      }
    )
  }

  async updateWorkout(workoutId: string, updates: Partial<WorkoutData>): Promise<WorkoutData> {
    return this.request<WorkoutData>(
      `/workouts/${workoutId}`,
      {
        method: 'PUT',
        body: JSON.stringify(updates)
      }
    )
  }

  async deleteWorkout(workoutId: string): Promise<void> {
    return this.request(`/workouts/${workoutId}`, {
      method: 'DELETE'
    })
  }

  /**
   * 运动库API
   */
  async getExercises(params: ExerciseSearchParams = {}): Promise<ApiExerciseResponse[]> {
    const queryParams = buildExerciseQueryParams(params);
    const queryString = queryParams.toString();
    return this.request<ApiExerciseResponse[]>(`/exercise/exercises${queryString ? `?${queryString}` : ''}`, {}, true);
  }

  async searchExercises(searchTerm: string, params: ExerciseSearchParams = {}): Promise<ApiExerciseResponse[]> {
    const queryParams = buildExerciseQueryParams(params);
    queryParams.append('search', searchTerm);
    const queryString = queryParams.toString();
    return this.request<ApiExerciseResponse[]>(`/exercise/exercises/search${queryString ? `?${queryString}` : ''}`, {}, true);
  }

  async getExercise(exerciseId: string): Promise<ApiExerciseResponse> {
    return this.request<ApiExerciseResponse>(`/exercise/exercises/${exerciseId}`, {}, true);
  }

  // 保留旧的方法以兼容现有代码
  async getExercisesLegacy(
    category?: string,
    muscleGroup?: string,
    equipment?: string
  ): Promise<Exercise[]> {
    const params = new URLSearchParams()
    if (category) params.append('category', category)
    if (muscleGroup) params.append('muscle_group', muscleGroup)
    if (equipment) params.append('equipment', equipment)

    const queryString = params.toString()
    return this.request<Exercise[]>(`/exercises${queryString ? `?${queryString}` : ''}`)
  }

  /**
   * 健康状态检查
   */
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.request<{ status: string; timestamp: string }>('/health', {}, true)
  }

  /**
   * 获取认证状态
   */
  isAuthenticated(): boolean {
    return !!this.accessToken
  }

  /**
   * 获取当前用户token
   */
  getAccessToken(): string | null {
    return this.accessToken
  }

  /**
   * 获取动作详情信息
   * @param exerciseId 动作ID
   * @returns 动作详情信息
   */
  async getExerciseDetail(exerciseId: number): Promise<ExerciseDetailResponse> {
    const endpoint = `/exercise/exercises/${exerciseId}/detail`;
    
    try {
      const response = await this.request<ExerciseDetailResponse>(endpoint);
      console.log('✅ 获取动作详情成功:', { exerciseId, response });
      return response;
    } catch (error) {
      console.error('❌ 获取动作详情失败:', error);
      throw new Error(`获取动作详情失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 构建视频文件的完整URL
   * @param videoFileName 视频文件名
   * @returns 完整的视频URL
   */
  buildVideoUrl(videoFileName: string): string {
    if (!videoFileName) return '';
    
    // 从baseURL中提取根域名，移除/api/v1路径
    const baseUrl = this.config.baseURL.replace('/api/v1', '');
    return `${baseUrl}/exercises/videos/${videoFileName}`;
  }

  /**
   * 公共请求方法，用于离线同步等场景
   */
  async publicRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    return this.request<T>(endpoint, options)
  }
}

// 导出单例实例
export const apiService = new ApiService()
export default apiService